<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>淘宝直播助手配置</title>
    <style>
        body {
            width: 400px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #ff6900;
            box-shadow: 0 0 0 2px rgba(255, 105, 0, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        button {
            flex: 1;
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-save {
            background: #ff6900;
            color: white;
        }
        
        .btn-save:hover {
            background: #e55a00;
        }
        
        .btn-save:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-reset {
            background: #f0f0f0;
            color: #666;
            border: 1px solid #ddd;
        }
        
        .btn-reset:hover {
            background: #e8e8e8;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px 12px;
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
        }
        
        .status.success {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }
        
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .help-text {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
            line-height: 1.4;
        }
        
        .current-url {
            background: #f8f9fa;
            padding: 10px 12px;
            border-radius: 4px;
            font-size: 13px;
            color: #666;
            border: 1px solid #e9ecef;
        }
        
        .current-url strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h3>🔧 Cookies上传地址</h3>
        <div class="form-group">
            <label for="baseUrl">服务器地址：</label>
            <input type="text" id="baseUrl" placeholder="请输入完整的服务器地址，如：http://localhost:3000">
        </div>
        
        <div class="button-group">
            <button id="saveBtn" class="btn-save">保存配置</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html> 