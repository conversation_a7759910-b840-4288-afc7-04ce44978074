// 存储键名
const STORAGE_KEY = 'taobao_helper_base_url';

// DOM元素
let baseUrlInput;
let saveBtn;
let statusDiv;

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 获取DOM元素
    baseUrlInput = document.getElementById('baseUrl');
    saveBtn = document.getElementById('saveBtn');
    statusDiv = document.getElementById('status');

    // 绑定事件
    saveBtn.addEventListener('click', handleSave);
    baseUrlInput.addEventListener('input', clearStatus);
    baseUrlInput.addEventListener('keypress', handleKeyPress);

    // 加载当前配置
    await loadCurrentConfig();
});

// 加载当前配置
async function loadCurrentConfig() {
    try {
        const result = await chrome.storage.sync.get([STORAGE_KEY]);
        const currentUrl = result[STORAGE_KEY] || '';
        
        // 设置输入框的值
        baseUrlInput.value = currentUrl;
        
        console.log('当前配置的URL:', currentUrl);
    } catch (error) {
        console.error('加载配置失败:', error);
        showStatus('加载配置失败', 'error');
    }
}

// 保存配置
async function handleSave() {
    const inputUrl = baseUrlInput.value.trim();
    
    // 验证输入
    // if (!inputUrl) {
    //     showStatus('请输入服务器地址', 'error');
    //     baseUrlInput.focus();
    //     return;
    // }

    // // 验证URL格式
    // if (!isValidUrl(inputUrl)) {
    //     showStatus('请输入有效的URL格式，如：http://localhost:3000', 'error');
    //     baseUrlInput.focus();
    //     return;
    // }

    // 禁用按钮，显示保存中
    saveBtn.disabled = true;
    saveBtn.textContent = '保存中...';

    try {
        // 保存到chrome存储
        await chrome.storage.sync.set({
            [STORAGE_KEY]: inputUrl
        });

        showStatus('配置保存成功！', 'success');
        console.log('URL配置已保存:', inputUrl);

        // 通知background脚本配置已更新
        try {
            await chrome.runtime.sendMessage({
                action: 'configUpdated',
                baseUrl: inputUrl
            });
        } catch (error) {
            console.log('通知background脚本失败:', error);
        }

    } catch (error) {
        console.error('保存配置失败:', error);
        showStatus('保存失败，请重试', 'error');
    } finally {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.textContent = '保存配置';
    }
}



// 处理键盘事件
function handleKeyPress(event) {
    if (event.key === 'Enter') {
        handleSave();
    }
}

// 清除状态信息
function clearStatus() {
    statusDiv.style.display = 'none';
    statusDiv.className = 'status';
}

// 显示状态信息
function showStatus(message, type = 'success') {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';

    // 3秒后自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

// 验证URL格式
function isValidUrl(string) {
    try {
        const url = new URL(string);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (error) {
        return false;
    }
}

// 导出函数供测试使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        isValidUrl,
        STORAGE_KEY
    };
} 