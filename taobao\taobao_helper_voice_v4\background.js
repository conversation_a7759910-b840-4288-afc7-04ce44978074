let enableLogging = false;
// 添加调试级别日志记录
function logDebug(message, data) {
  if (!enableLogging) return;
    if (data !== undefined) {
        console.log(`[DEBUG] ${message}`, data);
    } else {
        console.log(`[DEBUG] ${message}`);
    }
}

// 配置存储相关
const STORAGE_KEY = 'taobao_helper_base_url';
let currentBaseUrl = '';

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "openProtocol") {
        chrome.tabs.update(sender.tab.id, {url: request.url});
        sendResponse({ success: true });
    } else if (request.action === "configUpdated") {
        // 处理配置更新
        currentBaseUrl = request.baseUrl || '';
        logDebug('配置已更新，新的base URL:', currentBaseUrl);
        sendResponse({ success: true });
    }
    return true; // 保持消息通道开放
});

// 从存储中加载配置
async function loadConfig() {
    try {
        const result = await chrome.storage.sync.get([STORAGE_KEY]);
        currentBaseUrl = result[STORAGE_KEY] || '';
        logDebug('已加载配置，当前base URL:', currentBaseUrl);
    } catch (error) {
        logDebug('加载配置失败:', error);
        currentBaseUrl = '';
    }
}

// 构建完整的API URL
function buildApiUrl(endpoint) {
    // 检查是否有配置的base URL
    if (!currentBaseUrl) {
        logDebug('没有配置base URL，无法构建API URL');
        return null;
    }
    
    // 确保base URL末尾没有斜杠
    const baseUrl = currentBaseUrl.replace(/\/$/, '');
    // 确保endpoint开头有斜杠
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    return baseUrl + cleanEndpoint;
}

// 定时上传淘宝热搜cookie功能
const TAOBAO_DOMAIN_URL = "https://.taobao.com/";
const UPLOAD_INTERVAL = 30; // 30分钟

// 获取指定域名的cookies
async function getCookiesFromDomain(url) {
    try {
        const cookies = await chrome.cookies.getAll({
            url: url
        });
        return cookies;
    } catch (error) {
        logDebug(`获取 ${url} cookies失败:`, error);
        return [];
    }
}

// 获取所有淘宝相关的cookies
async function getAllTaobaoCookies() {
    try {
        // 只获取.taobao.com域名的cookies
        const domainCookies = await getCookiesFromDomain(TAOBAO_DOMAIN_URL);

        logDebug(`获取到 .taobao.com: ${domainCookies.length} 个cookies`);
        logDebug(`总计: ${domainCookies.length} 个cookies`);
        
        return domainCookies;
    } catch (error) {
        logDebug('获取淘宝cookies失败:', error);
        return [];
    }
}

// 将cookies数组转换为字符串格式
function formatCookies(cookies) {
    if (!cookies || cookies.length === 0) {
        return '';
    }
    
    return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
}



// 上传cookies到服务器
async function uploadCookies(cookies, anchorName) {
    try {
        // 检查是否有配置的URL
        if (!currentBaseUrl) {
            logDebug('没有配置服务器地址，跳过上传');
            return false;
        }

        const cookieValue = formatCookies(cookies);
        
        if (!cookieValue) {
            logDebug('没有有效的cookies需要上传');
            return false;
        }

        // 使用动态配置的URL
        const uploadUrl = buildApiUrl('/api/update-cookie');
        if (!uploadUrl) {
            logDebug('无法构建上传URL，跳过上传');
            return false;
        }
        
        logDebug('准备上传的cookie数据长度:', cookieValue.length);
        logDebug('上传到URL:', uploadUrl);

        const response = await fetch(uploadUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                anchorName: anchorName,
                cookieValue: cookieValue
            })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logDebug('淘宝cookies上传成功:', result.message);
                if (result.data) {
                    logDebug('更新信息:', result.data);
                }
                return true;
            } else {
                logDebug('cookies上传失败:', result.message);
                return false;
            }
        } else {
            logDebug('cookies上传失败:', response.status, response.statusText);
            return false;
        }
    } catch (error) {
        logDebug('上传cookies时发生错误:', error);
        return false;
    }
}

// 执行cookie上传任务
async function performCookieUpload() {
    // 检查是否有配置的URL
    if (!currentBaseUrl) {
        logDebug('没有配置服务器地址，跳过cookie上传任务');
        return;
    }

    logDebug('开始上传淘宝相关网站cookies...');
    const cookies = await getAllTaobaoCookies();
    
    if (cookies.length > 0) {
        logDebug(`总计获取到 ${cookies.length} 个cookies`);
        
        // 跳过验证，直接上传
        logDebug('跳过Cookie验证，直接开始上传...');
        
        const success = await uploadCookies(cookies);
        if (success) {
            logDebug('cookies上传任务完成');
        } else {
            logDebug('cookies上传任务失败');
        }
    } else {
        logDebug('未获取到淘宝相关cookies，可能需要先访问淘宝相关网站');
    }
}

// 创建定时器
function createCookieUploadAlarm() {
    // 清除已存在的定时器
    chrome.alarms.clear('cookieUpload');
    
    // 创建新的定时器，每1分钟执行一次
    chrome.alarms.create('cookieUpload', {
        delayInMinutes: 1, // 1分钟后首次执行
        periodInMinutes: UPLOAD_INTERVAL
    });
    
    logDebug(`淘宝cookie上传定时器已创建，每${UPLOAD_INTERVAL}分钟执行一次`);
}

// 监听定时器事件
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cookieUpload') {
        performCookieUpload();
    }
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(async () => {
    await loadConfig();
    createCookieUploadAlarm();
});

// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(async () => {
    await loadConfig();
    createCookieUploadAlarm();
    // 立即执行一次上传
    performCookieUpload();
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync' && changes[STORAGE_KEY]) {
        const newUrl = changes[STORAGE_KEY].newValue || '';
        if (newUrl !== currentBaseUrl) {
            currentBaseUrl = newUrl;
            logDebug('检测到配置变化，已更新base URL:', currentBaseUrl);
        }
    }
});
