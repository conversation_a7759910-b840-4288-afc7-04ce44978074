<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频播放功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .mock-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>音频播放功能测试</h1>
        
        <div class="info">
            <h3>功能说明：</h3>
            <ul>
                <li>在手卡显示的modal上新增了播放音频区域</li>
                <li>添加了播放按钮，点击后播放 D:/voice/itemId.wav</li>
                <li>显示播放进度条和时间</li>
                <li>播放完成后显示"已播放完毕"</li>
                <li>itemId.wav 代表当前讲解产品的id+后缀</li>
            </ul>
        </div>

        <h3>测试步骤：</h3>
        <ol>
            <li>点击下面的"测试手卡显示"按钮</li>
            <li>在弹出的手卡modal中，查看音频播放区域</li>
            <li>点击播放按钮测试音频播放功能</li>
            <li>观察进度条和状态显示</li>
        </ol>

        <div class="mock-item">
            <h4>模拟商品项 (itemId: 782366536941)</h4>
            <div class="list-item-content-title-item-name">测试商品标题</div>
            <div class="calculated-commission">佣金:￥10.50</div>
            <div data-rbd-drag-handle-draggable-id="782366536941"></div>
        </div>

        <button class="test-button" onclick="testCardModal()">测试手卡显示</button>
        <button class="test-button" onclick="testAudioPath()">测试音频路径设置</button>
        <button class="test-button" onclick="checkAudioFile()">检查音频文件</button>
        <button class="test-button" onclick="createTestAudio()">创建测试音频</button>
        
        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4>测试结果：</h4>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        // 模拟必要的全局函数和变量
        let enableLogging = true;
        
        function logDebug(message, data) {
            if (data !== undefined) {
                console.log(`[DEBUG] ${message}`, data);
            } else {
                console.log(`[DEBUG] ${message}`);
            }
        }

        // 模拟sendClearProtocol函数
        function sendClearProtocol() {
            console.log('调用清除协议');
        }

        // 模拟getCurrentLiveId函数
        function getCurrentLiveId() {
            return 'test-live-id-12345';
        }

        // 测试手卡modal显示
        function testCardModal() {
            // 创建模拟的listItem
            const mockListItem = document.querySelector('.mock-item');

            // 模拟showCustomCard函数的部分逻辑
            const liveId = getCurrentLiveId();

            // 清理之前的modal
            const existingModal = document.getElementById('custom-card-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // 创建CustomCardModalManager实例进行测试
            const modal = new TestCustomCardModalManager();

            // 设置测试音频路径
            modal.setAudioPath('782366536941');

            // 更新标题
            modal.updateTitle({
                title: '测试商品标题',
                commission: '佣金:￥10.50'
            });

            // 显示modal
            modal.show();

            // 模拟手卡内容
            setTimeout(() => {
                modal.updateCardInfo('这是一个测试手卡内容。\n\n产品特点：\n1. 高质量材料\n2. 精美设计\n3. 超值价格\n\n推荐理由：\n- 性价比高\n- 用户评价好\n- 售后服务完善', false, false, true);
            }, 1000);

            showTestResult('手卡modal已显示，请查看音频播放功能和调试信息');
        }

        // 测试音频路径设置
        function testAudioPath() {
            const testItemId = '782366536941';
            const expectedPath = `D:/voice/${testItemId}.wav`;

            showTestResult(`测试音频路径设置：<br>商品ID: ${testItemId}<br>音频路径: ${expectedPath}`);
        }

        // 检查音频文件是否存在
        function checkAudioFile() {
            const testItemId = '782366536941';
            const audioPath = `D:/voice/${testItemId}.wav`;

            // 创建一个临时的audio元素来测试文件是否存在
            const testAudio = document.createElement('audio');
            testAudio.preload = 'metadata';

            testAudio.addEventListener('loadedmetadata', () => {
                showTestResult(`✅ 音频文件存在且可访问<br>路径: ${audioPath}<br>时长: ${formatTime(testAudio.duration)}`);
            });

            testAudio.addEventListener('error', (e) => {
                let errorMsg = '未知错误';
                if (testAudio.error) {
                    switch(testAudio.error.code) {
                        case 1: errorMsg = '音频加载被中止'; break;
                        case 2: errorMsg = '网络错误或文件不存在'; break;
                        case 3: errorMsg = '音频解码错误'; break;
                        case 4: errorMsg = '音频格式不支持或文件不存在'; break;
                    }
                }
                showTestResult(`❌ 音频文件访问失败<br>路径: ${audioPath}<br>错误: ${errorMsg}<br><br>请确保：<br>1. 文件存在于指定路径<br>2. 文件格式为.wav<br>3. 浏览器有访问本地文件的权限`);
            });

            testAudio.src = audioPath;
            showTestResult('正在检查音频文件...');
        }

        // 创建测试音频文件的说明
        function createTestAudio() {
            const testItemId = '782366536941';
            const audioPath = `D:/voice/${testItemId}.wav`;

            showTestResult(`
                <h4>创建测试音频文件步骤：</h4>
                <ol>
                    <li>确保 D:/voice/ 目录存在</li>
                    <li>创建或复制一个.wav格式的音频文件</li>
                    <li>将文件重命名为: <strong>${testItemId}.wav</strong></li>
                    <li>完整路径应为: <strong>${audioPath}</strong></li>
                </ol>
                <p><strong>注意：</strong></p>
                <ul>
                    <li>音频文件必须是.wav格式</li>
                    <li>文件名必须与商品ID完全匹配</li>
                    <li>浏览器可能需要特殊权限才能访问本地文件</li>
                </ul>
                <p>创建文件后，点击"检查音频文件"按钮验证。</p>
            `);
        }

        // 格式化时间函数
        function formatTime(time) {
            if (isNaN(time)) return '00:00';
            const minutes = Math.floor(time / 60);
            const seconds = Math.floor(time % 60);
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 显示测试结果
        function showTestResult(message) {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('result-content');
            contentDiv.innerHTML = message;
            resultsDiv.style.display = 'block';
        }

        // 在页面加载完成后，确保必要的函数可用
        window.addEventListener('load', function() {
            console.log('测试页面加载完成');
        });
    </script>
    
    <!-- 测试版本的CustomCardModalManager类 -->
    <script>
        class TestCustomCardModalManager {
            constructor() {
                this.modal = null;
                this.contentArea = null;
                this.currentCardInfo = '';
                this.isPlaying = false;
                this.createModal();
            }

            createModal() {
                // 创建模态框容器
                this.modal = document.createElement('div');
                this.modal.id = 'custom-card-modal';
                this.modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: none;
                    z-index: 10000;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                `;

                // 创建模态框内容
                this.content = document.createElement('div');
                this.content.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 95%;
                    max-width: 1400px;
                    height: 80%;
                    max-height: 700px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                `;

                // 创建标题栏
                this.header = document.createElement('div');
                this.header.style.cssText = `
                    background: #f8f9fa;
                    border-bottom: 1px solid #e9ecef;
                    padding: 16px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-shrink: 0;
                `;

                this.title = document.createElement('div');
                this.title.innerHTML = 'AI手卡';
                this.title.style.cssText = `
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                `;

                this.closeButton = document.createElement('button');
                this.closeButton.innerHTML = '×';
                this.closeButton.style.cssText = `
                    background: #f5f5f5;
                    border: 1px solid #d9d9d9;
                    color: #666;
                    font-size: 20px;
                    cursor: pointer;
                    padding: 0;
                    width: 32px;
                    height: 32px;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;

                this.header.appendChild(this.title);
                this.header.appendChild(this.closeButton);

                // 创建内容区域
                this.body = document.createElement('div');
                this.body.style.cssText = `
                    flex: 1;
                    overflow-y: auto;
                    background: white;
                `;

                this.createAudioSection();
                this.createCardContent();

                // 创建底部按钮区域
                this.footer = document.createElement('div');
                this.footer.style.cssText = `
                    background: #f8f9fa;
                    border-top: 1px solid #e9ecef;
                    padding: 16px 20px;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    flex-shrink: 0;
                `;

                this.bottomCloseButton = document.createElement('button');
                this.bottomCloseButton.innerHTML = '关闭';
                this.bottomCloseButton.style.cssText = `
                    background: #dc3545;
                    border: 1px solid #dc3545;
                    color: white;
                    font-size: 14px;
                    cursor: pointer;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                `;

                this.footer.appendChild(this.bottomCloseButton);

                // 组装模态框
                this.content.appendChild(this.header);
                this.content.appendChild(this.body);
                this.content.appendChild(this.footer);
                this.modal.appendChild(this.content);

                // 添加到页面
                document.body.appendChild(this.modal);

                // 绑定事件
                this.bindEvents();
            }

            createAudioSection() {
                // 创建音频播放区域
                this.audioSection = document.createElement('div');
                this.audioSection.style.cssText = `
                    padding: 15px 20px;
                    border-bottom: 1px solid #e9ecef;
                    background: #f8f9fa;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                `;

                // 创建播放按钮
                this.playButton = document.createElement('button');
                this.playButton.innerHTML = '▶️ 播放';
                this.playButton.style.cssText = `
                    background: #007bff;
                    border: 1px solid #007bff;
                    color: white;
                    font-size: 14px;
                    cursor: pointer;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                `;

                // 创建进度条容器
                this.progressContainer = document.createElement('div');
                this.progressContainer.style.cssText = `
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                `;

                // 创建进度条
                this.progressBar = document.createElement('div');
                this.progressBar.style.cssText = `
                    flex: 1;
                    height: 6px;
                    background: #e9ecef;
                    border-radius: 3px;
                    overflow: hidden;
                    position: relative;
                    cursor: pointer;
                `;

                this.progressFill = document.createElement('div');
                this.progressFill.style.cssText = `
                    height: 100%;
                    background: #007bff;
                    width: 0%;
                    transition: width 0.1s ease;
                `;

                this.progressBar.appendChild(this.progressFill);

                // 创建时间显示
                this.timeDisplay = document.createElement('span');
                this.timeDisplay.textContent = '00:00 / 00:00';
                this.timeDisplay.style.cssText = `
                    font-size: 12px;
                    color: #666;
                    min-width: 80px;
                    text-align: center;
                `;

                // 创建状态显示
                this.statusDisplay = document.createElement('span');
                this.statusDisplay.textContent = '';
                this.statusDisplay.style.cssText = `
                    font-size: 12px;
                    color: #28a745;
                    font-weight: 500;
                `;

                // 创建调试信息显示
                this.debugDisplay = document.createElement('div');
                this.debugDisplay.style.cssText = `
                    font-size: 11px;
                    color: #666;
                    margin-top: 5px;
                    padding: 5px;
                    background: #f1f1f1;
                    border-radius: 3px;
                    font-family: monospace;
                `;

                // 组装进度条容器
                this.progressContainer.appendChild(this.progressBar);
                this.progressContainer.appendChild(this.timeDisplay);
                this.progressContainer.appendChild(this.statusDisplay);

                // 组装音频区域
                this.audioSection.appendChild(this.playButton);
                this.audioSection.appendChild(this.progressContainer);
                this.audioSection.appendChild(this.debugDisplay);

                // 创建隐藏的audio元素
                this.audioElement = document.createElement('audio');
                this.audioElement.preload = 'metadata';

                // 将音频区域添加到body
                this.body.appendChild(this.audioSection);
            }

            createCardContent() {
                // 创建手卡内容显示区域
                this.cardContent = document.createElement('div');
                this.cardContent.style.cssText = `
                    padding: 20px;
                    line-height: 1.8;
                    font-size: 24px;
                    color: #1c1e21;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    flex: 1;
                    overflow-y: auto;
                `;

                this.body.appendChild(this.cardContent);
            }

            bindEvents() {
                // 关闭按钮事件
                this.closeButton.addEventListener('click', () => {
                    this.stopAudio();
                    this.hide();
                });

                // 点击背景关闭
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.stopAudio();
                        this.hide();
                    }
                });

                // 底部关闭按钮事件
                this.bottomCloseButton.addEventListener('click', () => {
                    this.stopAudio();
                    this.hide();
                });

                // 播放按钮事件
                this.playButton.addEventListener('click', () => {
                    this.toggleAudio();
                });

                // 音频事件监听
                this.audioElement.addEventListener('loadstart', () => {
                    this.statusDisplay.textContent = '加载中...';
                    this.statusDisplay.style.color = '#ffc107';
                    this.updateDebugInfo('开始加载音频');
                });

                this.audioElement.addEventListener('loadedmetadata', () => {
                    this.statusDisplay.textContent = '准备就绪';
                    this.statusDisplay.style.color = '#28a745';
                    this.updateTimeDisplay(0, this.audioElement.duration);
                    this.updateDebugInfo(`音频时长: ${this.formatTime(this.audioElement.duration)}`);
                });

                this.audioElement.addEventListener('canplay', () => {
                    this.statusDisplay.textContent = '可以播放';
                    this.statusDisplay.style.color = '#28a745';
                });

                this.audioElement.addEventListener('play', () => {
                    this.isPlaying = true;
                    this.playButton.innerHTML = '⏸️ 暂停';
                    this.playButton.style.backgroundColor = '#dc3545';
                    this.playButton.style.borderColor = '#dc3545';
                    this.statusDisplay.textContent = '播放中';
                    this.statusDisplay.style.color = '#007bff';
                    this.updateDebugInfo('开始播放');
                });

                this.audioElement.addEventListener('pause', () => {
                    this.isPlaying = false;
                    this.playButton.innerHTML = '▶️ 播放';
                    this.playButton.style.backgroundColor = '#007bff';
                    this.playButton.style.borderColor = '#007bff';
                    this.statusDisplay.textContent = '已暂停';
                    this.statusDisplay.style.color = '#6c757d';
                });

                this.audioElement.addEventListener('ended', () => {
                    this.isPlaying = false;
                    this.playButton.innerHTML = '▶️ 播放';
                    this.playButton.style.backgroundColor = '#007bff';
                    this.playButton.style.borderColor = '#007bff';
                    this.statusDisplay.textContent = '已播放完毕';
                    this.statusDisplay.style.color = '#28a745';
                    this.progressFill.style.width = '100%';
                    this.updateTimeDisplay(this.audioElement.duration, this.audioElement.duration);
                    this.updateDebugInfo('播放完成');
                });

                this.audioElement.addEventListener('timeupdate', () => {
                    if (this.audioElement.duration) {
                        const progress = (this.audioElement.currentTime / this.audioElement.duration) * 100;
                        this.progressFill.style.width = progress + '%';
                        this.updateTimeDisplay(this.audioElement.currentTime, this.audioElement.duration);
                    }
                });

                this.audioElement.addEventListener('error', (e) => {
                    this.statusDisplay.textContent = '播放失败';
                    this.statusDisplay.style.color = '#dc3545';
                    this.playButton.innerHTML = '▶️ 播放';
                    this.playButton.style.backgroundColor = '#007bff';
                    this.playButton.style.borderColor = '#007bff';
                    this.isPlaying = false;

                    let errorMsg = '未知错误';
                    if (this.audioElement.error) {
                        switch(this.audioElement.error.code) {
                            case 1: errorMsg = '音频加载被中止'; break;
                            case 2: errorMsg = '网络错误'; break;
                            case 3: errorMsg = '音频解码错误'; break;
                            case 4: errorMsg = '音频格式不支持或文件不存在'; break;
                        }
                    }
                    this.updateDebugInfo(`播放错误: ${errorMsg}`);
                    console.error('音频播放错误:', e, this.audioElement.error);
                });

                // 进度条点击事件
                this.progressBar.addEventListener('click', (e) => {
                    if (this.audioElement.duration) {
                        const rect = this.progressBar.getBoundingClientRect();
                        const clickX = e.clientX - rect.left;
                        const progress = clickX / rect.width;
                        this.audioElement.currentTime = progress * this.audioElement.duration;
                        this.updateDebugInfo(`跳转到: ${this.formatTime(this.audioElement.currentTime)}`);
                    }
                });
            }

            show() {
                if (this.modal) {
                    this.modal.style.display = 'block';
                    this.modal.style.opacity = '1';
                    this.modal.style.transform = 'scale(1)';
                }
            }

            hide() {
                if (this.modal) {
                    this.modal.style.display = 'none';
                }
            }

            // 设置音频文件路径
            setAudioPath(itemId) {
                if (itemId) {
                    this.currentItemId = itemId;
                    this.audioPath = `D:/voice/${itemId}.wav`;
                    this.audioElement.src = this.audioPath;
                    this.statusDisplay.textContent = '音频已设置';
                    this.statusDisplay.style.color = '#28a745';
                    this.updateDebugInfo(`音频路径: ${this.audioPath}`);
                    console.log('设置音频路径:', this.audioPath);
                } else {
                    this.statusDisplay.textContent = '无音频文件';
                    this.statusDisplay.style.color = '#6c757d';
                    this.updateDebugInfo('未设置音频路径');
                }
            }

            // 切换音频播放/暂停
            toggleAudio() {
                if (!this.audioElement.src) {
                    this.statusDisplay.textContent = '未找到音频文件';
                    this.statusDisplay.style.color = '#dc3545';
                    this.updateDebugInfo('音频源未设置');
                    return;
                }

                if (this.isPlaying) {
                    this.audioElement.pause();
                } else {
                    this.audioElement.play().catch(error => {
                        console.error('音频播放失败:', error);
                        this.statusDisplay.textContent = '播放失败';
                        this.statusDisplay.style.color = '#dc3545';
                        this.updateDebugInfo(`播放失败: ${error.message}`);
                    });
                }
            }

            // 停止音频播放
            stopAudio() {
                if (this.audioElement) {
                    this.audioElement.pause();
                    this.audioElement.currentTime = 0;
                    this.progressFill.style.width = '0%';
                    this.updateTimeDisplay(0, this.audioElement.duration || 0);
                }
            }

            // 更新时间显示
            updateTimeDisplay(currentTime, duration) {
                this.timeDisplay.textContent = `${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
            }

            // 格式化时间
            formatTime(time) {
                if (isNaN(time)) return '00:00';
                const minutes = Math.floor(time / 60);
                const seconds = Math.floor(time % 60);
                return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            // 更新调试信息
            updateDebugInfo(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.debugDisplay.textContent = `[${timestamp}] ${message}`;
            }

            // 更新手卡标题
            updateTitle(productInfo) {
                if (!this.title || !productInfo) return;

                let titleText = 'AI手卡';

                if (productInfo.title) {
                    titleText = productInfo.title;
                }

                if (productInfo.commission) {
                    titleText += ` | ${productInfo.commission}`;
                }

                this.title.innerHTML = titleText;
            }

            // 更新手卡内容
            updateCardInfo(cardInfo) {
                if (!this.cardContent) return;
                this.cardContent.textContent = cardInfo;
            }
        }
    </script>
</body>
</html>
