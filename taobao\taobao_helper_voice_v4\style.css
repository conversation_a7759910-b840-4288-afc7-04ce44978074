.smart-card-btn {
  margin: 0 4px;
  background-color: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 4px !important;
  color: #333 !important;
  font-size: 14px !important;
  height: 32px !important;
  line-height: 24px !important;
}

.smart-card-btn:hover {
  background-color: #e8e8e8 !important;
  color: #333 !important;
}

.smart-card-btn span {
  color: #333 !important;
}

.tbla-drawer-content-wrapper {
  width: 80% !important;
}

/* 让商品讲解内容区域更宽 */
.live-ai-kit-common-script-card-script-card-content {
  width: 100% !important;
  max-width: none !important;
}

/* 设置生成内容区域更宽 */
.lak-sc-common-generation {
  width: 100% !important;
  max-width: none !important;
  padding: 0 !important;
}

/* 设置讲解脚本内容区域更宽和字体大小 */
.lak-sc-common-generation-content {
  width: 100% !important;
  max-width: none !important;
  padding: 20px !important;
  box-sizing: border-box !important;
  font-size: 24px !important;
}

/* 设置打字机区域更宽和字体样式 */
.lak-typewriter-plus {
  width: 100% !important;
  max-width: none !important;
  --TP-theme-text-font-size: 24px !important;
  --TP-theme-title-font-size: 28px !important;
}

/* 设置打字机内容更宽和字体大小 */
.lak-typewriter-plus-content {
  width: 100% !important;
  max-width: none !important;
  padding: 0 !important;
  font-size: 24px !important;
}

/* 设置富文本编辑器更宽 */
.lak-ui-rich-text {
  width: 100% !important;
  max-width: none !important;
}

/* 设置整个脚本卡片区域更宽 */
.live-ai-kit-common-script-card {
  width: 100% !important;
  max-width: none !important;
}

/* 移除可能限制宽度的边距和内边距 */
.lak-sc-common-generation-content-defaultheight {
  margin: 0 !important;
  padding: 20px !important;
}

/* 设置打字机编辑器内容的字体大小和样式 */
.lak-typewriter-plus .tiptap {
  font-size: 24px !important;
  line-height: 1.6 !important;
}

/* 设置兼容旧版本打字机内容的字体大小 */
.lak-typewriter-content {
  font-size: 24px !important;
}

/* 设置手卡内容中的段落字体大小 */
.lak-typewriter-plus .tiptap p {
  font-size: 24px !important;
  line-height: 1.6 !important;
  margin-bottom: 16px !important;
}

/* 设置手卡内容中的标题字体大小 */
.lak-typewriter-plus .tiptap h3 {
  font-size: 28px !important;
  font-weight: bold !important;
  margin-bottom: 12px !important;
  margin-top: 20px !important;
}

/* 设置关键词高亮的字体大小 */
.lak-typewriter-plus .tiptap strong[data-keyword="true"] {
  font-size: 24px !important;
  color: #1890ff !important;
  font-weight: bold !important;
}
.lak-new-script-card-bottom-aside{
  display: none !important;
}